/**
 * アプリケーション設定の一元管理
 * 環境変数と設定値を管理する中央設定ファイル
 */

// 環境判定
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';
export const isTest = process.env.NODE_ENV === 'test';

// API設定
export const apiConfig = {
  // APIベースURL（プロダクション環境では環境変数から取得）
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
  
  // モックモードの判定（環境変数またはローカルホスト判定）
  useMockData: process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' || 
               (!isProduction && !process.env.NEXT_PUBLIC_API_BASE_URL),
  
  // APIタイムアウト設定（ミリ秒）
  timeout: 30000,
  
  // リトライ設定
  retry: {
    count: 3,
    delay: 1000,
  }
};

// Azure Speech Service設定
export const azureConfig = {
  speechKey: process.env.NEXT_PUBLIC_AZURE_SPEECH_KEY || '',
  speechRegion: process.env.NEXT_PUBLIC_AZURE_SPEECH_REGION || '',
  
  // アバター設定
  avatar: {
    defaultAvatarId: 'lisa',
    videoFormat: 'webm',
    backgroundColor: '#FFFFFFFF',
  },
  
  // 音声設定
  voice: {
    defaultVoice: 'ja-JP-NanamiNeural',
    pitch: '+0%',
    rate: '+0%',
  },

  speechRecognition: {
    language: 'ja-JP',
    continuous: true,
    interimResults: true,
    maxAlternatives: 1,
    profanityFilter: false,
    enableDictation: true,
    audioFormat: 'wav',
    sampleRate: 16000,
    channels: 1
  }
};

// WebRTC設定
export const webRTCConfig = {
  // ICEサーバー設定（Azure通信サービスから取得）
  iceServers: [],
  
  // 接続タイムアウト（ミリ秒）
  connectionTimeout: 10000,
  
  // 再接続設定
  reconnect: {
    enabled: true,
    maxAttempts: 3,
    delay: 2000,
  }
};

// UI設定
export const uiConfig = {
  // アニメーション設定
  animation: {
    defaultDuration: 300,
    defaultEasing: 'easeOut',
  },
  
  // レスポンシブブレークポイント
  breakpoints: {
    sm: '30em',  // 480px
    md: '48em',  // 768px
    lg: '62em',  // 992px
    xl: '80em',  // 1280px
  },
  
  // テーマカラー
  theme: {
    primary: '#2B6CB0',
    secondary: '#1A365D',
    success: '#48BB78',
    warning: '#F6AD55',
    error: '#FC8181',
  }
};

// 面接設定
export const interviewConfig = {
  // 最大面接時間（秒）
  maxDuration: 3600, // 1時間
  
  // 録音設定
  recording: {
    maxDuration: 300, // 5分
    audioFormat: 'webm',
    sampleRate: 16000,
  },
  
  // フィードバック設定
  feedback: {
    minScore: 0,
    maxScore: 10,
    displayDelay: 1000, // ミリ秒
  },
  
  // 質問設定
  questions: {
    maxRetries: 2,
    timeLimit: 180, // 3分
  }
};

// ローカルストレージキー
export const storageKeys = {
  user: 'demo_user',
  interviewData: 'interview_data',
  practiceHistory: 'practice_history',
  settings: 'user_settings',
  authToken: 'auth_token',
};

// 外部URL
export const externalUrls = {
  // management: isProduction ? process.env.NEXT_PUBLIC_AGENT_URL : 'http://localhost:3001',
  management: process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:3001', 
  documentation: 'https://docs.mensetsu-kun.com',
  support: 'https://support.mensetsu-kun.com',
};

// ログ設定
export const logConfig = {
  // ログレベル（production環境では'error'のみ）
  level: isProduction ? 'error' : 'debug',
  
  // コンソールログの有効/無効
  enableConsoleLog: !isProduction,
  
  // リモートログ送信
  enableRemoteLogging: isProduction,
};

// 機能フラグ
export const featureFlags = {
  // 新機能のフラグ管理
  enableAdvancedAnalytics: process.env.NEXT_PUBLIC_ENABLE_ADVANCED_ANALYTICS === 'true',
  enableVideoRecording: process.env.NEXT_PUBLIC_ENABLE_VIDEO_RECORDING === 'true',
  enableAICoaching: process.env.NEXT_PUBLIC_ENABLE_AI_COACHING === 'true',
  enableMultiLanguage: process.env.NEXT_PUBLIC_ENABLE_MULTI_LANGUAGE === 'true',
};

// バリデーション：必須環境変数のチェック
export const validateConfig = () => {
  const errors: string[] = [];
  
  if (!azureConfig.speechKey) {
    errors.push('NEXT_PUBLIC_AZURE_SPEECH_KEY is not set');
  }
  
  if (!azureConfig.speechRegion) {
    errors.push('NEXT_PUBLIC_AZURE_SPEECH_REGION is not set');
  }
  
  if (isProduction && !process.env.NEXT_PUBLIC_API_BASE_URL) {
    errors.push('NEXT_PUBLIC_API_BASE_URL is not set in production');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// デフォルトエクスポート
const config = {
  isDevelopment,
  isProduction,
  isTest,
  api: apiConfig,
  azure: azureConfig,
  webRTC: webRTCConfig,
  ui: uiConfig,
  interview: interviewConfig,
  storage: storageKeys,
  urls: externalUrls,
  log: logConfig,
  features: featureFlags,
  validate: validateConfig,
};

export default config;