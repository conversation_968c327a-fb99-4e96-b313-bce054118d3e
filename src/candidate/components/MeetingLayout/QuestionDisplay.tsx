import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  Progress,
  IconButton,
  useBreakpointValue,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaMicrophone, FaVolumeUp, FaMicrophoneSlash } from 'react-icons/fa';
import { HiSparkles } from 'react-icons/hi';
import { useInterview } from '../../contexts/InterviewContext';
import { azureSpeechService, SpeechRecognitionResult } from '../../services/azureSpeechService';
import { interviewSocket } from '../../services/interviewSocket';

interface QuestionDisplayProps {
  currentQuestion: any;
  currentQuestionIndex: number;
  totalQuestions: number;
  isProcessingAnswer: boolean;
  onAnswerQuestion: () => void;
}

export const QuestionDisplay: React.FC<QuestionDisplayProps> = ({
  currentQuestion,
  currentQuestionIndex,
  totalQuestions,
  isProcessingAnswer,
  onAnswerQuestion,
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false });
  const { speakText } = useInterview();

  // Speech recognition states
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [autoStarted, setAutoStarted] = useState(false);
  const finalTranscriptRef = useRef('');
  const isProcessingRef = useRef(false);

  const handleSpeakQuestion = () => {
    speakText(currentQuestion?.text || currentQuestion);
  };

  // Auto-start speech recognition when question changes
  useEffect(() => {
    if (currentQuestion && !autoStarted && !isProcessingAnswer) {
      const timer = setTimeout(() => {
        startListening();
        setAutoStarted(true);
      }, 1500); // Delay 1.5s để user đọc câu hỏi

      return () => clearTimeout(timer);
    }
  }, [currentQuestion, autoStarted, isProcessingAnswer]);

  // Reset auto-start when question changes
  useEffect(() => {
    setAutoStarted(false);
    setTranscript('');
    setError(null);
    finalTranscriptRef.current = '';
  }, [currentQuestion]);

  const startListening = async () => {
    try {
      setError(null);
      setTranscript('');
      finalTranscriptRef.current = '';

      const success = await azureSpeechService.startRecognition(
        handleRecognitionResult,
        handleRecognitionError,
        handleRecognitionStart,
        handleRecognitionEnd
      );

      if (success) {
        setIsListening(true);
      }
    } catch (error) {
      setError(`Failed to start speech recognition: ${error}`);
    }
  };

  const stopListening = () => {
    azureSpeechService.stopRecognition();
    setIsListening(false);
  };

  const handleRecognitionResult = (result: SpeechRecognitionResult) => {
    if (result.isFinal) {
      finalTranscriptRef.current = result.transcript;
      setTranscript(result.transcript);
      setConfidence(result.confidence);

      // Auto-submit after getting final result
      if (!isProcessingRef.current && result.transcript.trim()) {
        isProcessingRef.current = true;

        // Send response_answer via socket
        interviewSocket.sendResponseAnswer(
          result.transcript,
          result.confidence,
          currentQuestion?.id
        );

        // Trigger the answer processing
        setTimeout(() => {
          onAnswerQuestion();
          isProcessingRef.current = false;
        }, 500);
      }
    } else {
      setTranscript(result.transcript);
    }
  };

  const handleRecognitionError = (error: string) => {
    setError(error);
    setIsListening(false);
  };

  const handleRecognitionStart = () => {
    setIsListening(true);
  };

  const handleRecognitionEnd = () => {
    setIsListening(false);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      azureSpeechService.cleanup();
    };
  }, []);

  return (
    <AnimatePresence>
      {currentQuestion && (
        <motion.div
          initial={{ x: -100, opacity: 0, scale: 0.9 }}
          animate={{ x: 0, opacity: 1, scale: 1 }}
          exit={{ x: 100, opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.6, type: 'spring', stiffness: 300 }}
          style={{
            position: 'fixed',
            bottom: '180px',
            left: '40px',
            width: '400px',
            zIndex: 20,
          }}
        >
          <Box
            bg="rgba(15, 23, 42, 0.8)"
            backdropFilter="blur(30px) saturate(180%)"
            borderRadius="3xl"
            p={8}
            border="2px solid rgba(59, 130, 246, 0.3)"
            boxShadow="0 25px 50px rgba(0, 0, 0, 0.4)"
            position="relative"
            overflow="hidden"
          >
            {/* Background Glow Effect */}
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              bgGradient="linear(45deg, transparent, rgba(59, 130, 246, 0.1), transparent)"
              borderRadius="3xl"
              zIndex={-1}
            />

            <VStack spacing={6} align="stretch">
              {/* Error Alert */}
              {error && (
                <Alert status="error" borderRadius="md" bg="rgba(254, 226, 226, 0.1)" border="1px solid rgba(239, 68, 68, 0.3)">
                  <AlertIcon color="red.400" />
                  <Text color="red.200" fontSize="sm">{error}</Text>
                </Alert>
              )}

              <HStack justify="space-between" flexWrap="wrap">
                <HStack spacing={3}>
                  <Badge
                    bg="linear-gradient(45deg, #3b82f6, #8b5cf6)"
                    color="white"
                    fontSize="sm"
                    px={4}
                    py={2}
                    borderRadius="full"
                    fontWeight="bold"
                    boxShadow="0 4px 15px rgba(59, 130, 246, 0.3)"
                  >
                    <HiSparkles style={{ marginRight: '6px' }} />
                    {currentQuestion?.category || 'Interview'}
                  </Badge>
                  <Badge
                    bg={
                      currentQuestion?.difficulty === 'expert'
                        ? 'linear-gradient(45deg, #ef4444, #dc2626)'
                        : currentQuestion?.difficulty === 'hard'
                        ? 'linear-gradient(45deg, #f97316, #ea580c)'
                        : 'linear-gradient(45deg, #eab308, #ca8a04)'
                    }
                    color="white"
                    fontSize="sm"
                    px={4}
                    py={2}
                    borderRadius="full"
                    fontWeight="bold"
                    boxShadow="0 4px 15px rgba(234, 179, 8, 0.3)"
                  >
                    {currentQuestion?.difficulty?.toUpperCase() || 'HARD'}
                  </Badge>
                  {/* Speech Status Badge */}
                  {isListening && (
                    <Badge
                      bg="linear-gradient(45deg, #10b981, #059669)"
                      color="white"
                      fontSize="sm"
                      px={4}
                      py={2}
                      borderRadius="full"
                      fontWeight="bold"
                      boxShadow="0 4px 15px rgba(16, 185, 129, 0.3)"
                    >
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                        style={{ display: 'flex', alignItems: 'center', marginRight: '6px' }}
                      >
                        <FaMicrophone />
                      </motion.div>
                      LISTENING
                    </Badge>
                  )}
                </HStack>
                <Text color="whiteAlpha.800" fontSize="sm" fontWeight="medium">
                  {currentQuestionIndex + 1} / {totalQuestions}
                </Text>
              </HStack>

              {/* Question text with speaker button */}
              <HStack align="flex-start" spacing={3}>
                <Text
                  color="white"
                  fontSize={isMobile ? 'lg' : 'xl'}
                  fontWeight="600"
                  lineHeight="tall"
                  letterSpacing="wide"
                  flex="1"
                >
                  {currentQuestion?.text || currentQuestion}
                </Text>
                <IconButton
                  aria-label="Speak question"
                  icon={<FaVolumeUp />}
                  size="sm"
                  bg="rgba(59, 130, 246, 0.2)"
                  color="white"
                  borderRadius="full"
                  _hover={{
                    bg: 'rgba(59, 130, 246, 0.4)',
                    transform: 'scale(1.1)',
                  }}
                  onClick={handleSpeakQuestion}
                />
              </HStack>

              {/* Transcript Preview */}
              {(transcript || isListening) && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Box
                    bg={isListening ? "rgba(16, 185, 129, 0.1)" : "rgba(59, 130, 246, 0.1)"}
                    border={`1px solid ${isListening ? "rgba(16, 185, 129, 0.3)" : "rgba(59, 130, 246, 0.3)"}`}
                    borderRadius="xl"
                    p={4}
                    mt={2}
                    position="relative"
                    overflow="hidden"
                  >
                    {/* Animated background for listening state */}
                    {isListening && (
                      <Box
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        bgGradient="linear(90deg, transparent, rgba(16, 185, 129, 0.05), transparent)"
                        borderRadius="xl"
                        zIndex={-1}
                      >
                        <motion.div
                          animate={{ x: ['-100%', '100%'] }}
                          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                          style={{
                            width: '100%',
                            height: '100%',
                            background: 'linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent)',
                          }}
                        />
                      </Box>
                    )}

                    <HStack spacing={2} mb={2}>
                      {isListening ? (
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                        >
                          <FaMicrophone color="#10b981" size={14} />
                        </motion.div>
                      ) : (
                        <FaMicrophone color="#3b82f6" size={14} />
                      )}
                      <Text
                        color={isListening ? "green.300" : "blue.300"}
                        fontSize="sm"
                        fontWeight="medium"
                      >
                        {isListening ? 'Listening...' : 'Your Response'}
                        {confidence > 0 && ` (${Math.round(confidence * 100)}% confidence)`}
                      </Text>
                    </HStack>
                    <Text
                      color="white"
                      fontSize="md"
                      fontStyle={transcript === finalTranscriptRef.current ? 'normal' : 'italic'}
                      minH="24px"
                    >
                      {transcript || (isListening ? 'Speak now...' : 'Ready to listen')}
                    </Text>
                  </Box>
                </motion.div>
              )}

              <Box>
                <Progress
                  value={((currentQuestionIndex + 1) / totalQuestions) * 100}
                  bg="rgba(255, 255, 255, 0.1)"
                  borderRadius="full"
                  size="lg"
                  sx={{
                    '& > div': {
                      background:
                        'linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899)',
                      borderRadius: 'full',
                      boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)',
                    },
                  }}
                />
                <Text
                  color="whiteAlpha.600"
                  fontSize="xs"
                  mt={2}
                  textAlign="center"
                >
                  Interview Progress •{' '}
                  {Math.round(
                    ((currentQuestionIndex + 1) / totalQuestions) * 100
                  )}
                  % Complete
                </Text>
              </Box>

              {/* Auto Speech Recognition Status */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  bg={
                    isListening
                      ? "linear-gradient(45deg, #10b981, #059669)"
                      : isProcessingAnswer
                      ? "linear-gradient(45deg, #8b5cf6, #7c3aed)"
                      : "linear-gradient(45deg, #3b82f6, #8b5cf6)"
                  }
                  color="white"
                  size={isMobile ? 'lg' : 'xl'}
                  borderRadius="2xl"
                  px={10}
                  py={6}
                  fontSize="lg"
                  fontWeight="bold"
                  leftIcon={
                    isListening ? (
                      <motion.div
                        animate={{ scale: [1, 1.3, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      >
                        <FaMicrophone />
                      </motion.div>
                    ) : isProcessingAnswer ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      >
                        <HiSparkles />
                      </motion.div>
                    ) : (
                      <FaMicrophoneSlash />
                    )
                  }
                  isLoading={isProcessingAnswer}
                  loadingText="Processing with AI..."
                  onClick={isListening ? stopListening : startListening}
                  _hover={{
                    transform: 'translateY(-2px)',
                    boxShadow: isListening
                      ? '0 15px 30px rgba(16, 185, 129, 0.4)'
                      : '0 15px 30px rgba(59, 130, 246, 0.4)',
                  }}
                  _active={{ transform: 'translateY(0px)' }}
                  transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                  boxShadow={
                    isListening
                      ? '0 10px 25px rgba(16, 185, 129, 0.3)'
                      : '0 10px 25px rgba(59, 130, 246, 0.3)'
                  }
                  border="1px solid rgba(255, 255, 255, 0.2)"
                  disabled={isProcessingAnswer}
                >
                  {isProcessingAnswer ? (
                    <HStack spacing={2}>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: 'linear',
                        }}
                      >
                        <HiSparkles />
                      </motion.div>
                      <Text>AI Processing...</Text>
                    </HStack>
                  ) : isListening ? (
                    <HStack spacing={2}>
                      <Text>🎤 Listening...</Text>
                    </HStack>
                  ) : (
                    <HStack spacing={2}>
                      <Text>🔇 Auto Speech Ready</Text>
                    </HStack>
                  )}
                </Button>
              </motion.div>
            </VStack>
          </Box>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
