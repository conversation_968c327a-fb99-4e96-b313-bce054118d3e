import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Text, VStack, Progress, Alert, AlertIcon } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { azureSpeechService, SpeechRecognitionResult } from '../services/azureSpeechService';
import { interviewSocket } from '../services/interviewSocket';

interface SpeechRecognitionInputProps {
  onSubmit: (transcript: string) => void;
  isLoading?: boolean;
  sessionId: string;
}

export const SpeechRecognitionInput: React.FC<SpeechRecognitionInputProps> = ({
  onSubmit,
  isLoading = false,
  sessionId
}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  const finalTranscriptRef = useRef('');
  const isProcessingRef = useRef(false);

  const startListening = async () => {
    try {
      setError(null);
      setTranscript('');
      finalTranscriptRef.current = '';
      
      const success = await azureSpeechService.startRecognition(
        handleRecognitionResult,
        handleRecognitionError,
        handleRecognitionStart,
        handleRecognitionEnd
      );

      if (success) {
        setIsListening(true);
        interviewSocket.sendSpeechStart();
      }
    } catch (error) {
      setError(`Failed to start speech recognition: ${error}`);
    }
  };

  const stopListening = () => {
    azureSpeechService.stopRecognition();
    setIsListening(false);
    interviewSocket.sendSpeechEnd();
  };

  const handleRecognitionResult = (result: SpeechRecognitionResult) => {
    if (result.isFinal) {
      finalTranscriptRef.current = result.transcript;
      setTranscript(result.transcript);
      setConfidence(result.confidence);
      
      // Send to backend via socket
      interviewSocket.sendSpeechResponse(
        result.transcript,
        result.confidence,
        true
      );

      // Auto-submit after a short delay
      if (!isProcessingRef.current) {
        isProcessingRef.current = true;
        setTimeout(() => {
          onSubmit(result.transcript);
          isProcessingRef.current = false;
        }, 1000);
      }
    } else {
      setTranscript(result.transcript);
    }
  };

  const handleRecognitionError = (error: string) => {
    setError(error);
    setIsListening(false);
  };

  const handleRecognitionStart = () => {
    setIsListening(true);
  };

  const handleRecognitionEnd = () => {
    setIsListening(false);
  };

  useEffect(() => {
    return () => {
      azureSpeechService.cleanup();
    };
  }, []);

  return (
    <VStack spacing={4} w="full">
      {error && (
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          {error}
        </Alert>
      )}

      <Box w="full">
        <Button
          colorScheme={isListening ? 'red' : 'primary'}
          onClick={isListening ? stopListening : startListening}
          isLoading={isLoading}
          size="lg"
          w="full"
          leftIcon={<Text>��</Text>}
        >
          {isListening ? 'Dừng ghi âm' : 'Bắt đầu ghi âm'}
        </Button>
      </Box>

      {isListening && (
        <motion.div
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Text fontSize="4xl">��️</Text>
        </motion.div>
      )}

      {transcript && (
        <Box w="full" p={4} bg="gray.50" borderRadius="md">
          <Text fontSize="sm" color="gray.600" mb={2}>
            {isListening ? 'Đang nhận dạng...' : 'Kết quả cuối cùng:'}
          </Text>
          <Text fontSize="md" fontWeight="medium">
            {transcript}
          </Text>
          {confidence > 0 && (
            <Progress 
              value={confidence * 100} 
              size="sm" 
              mt={2}
              colorScheme={confidence > 0.8 ? 'green' : confidence > 0.6 ? 'yellow' : 'red'}
            />
          )}
        </Box>
      )}
    </VStack>
  );
};
