{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx"}, "dependencies": {"@azure/communication-calling": "^1.35.1", "@azure/communication-common": "^2.3.1", "@azure/communication-identity": "^1.3.1", "@azure/communication-react": "^1.27.1", "@babylonjs/core": "^6.49.0", "@babylonjs/gui": "^6.49.0", "@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fluentui/react-components": "^9.64.0", "@mensetsu-kun/shared": "workspace:^1.0.0", "@types/axios": "^0.9.36", "axios": "^1.9.0", "dotenv": "^16.5.0", "framer-motion": "^11.18.2", "microsoft-cognitiveservices-speech-sdk": "^1.44.1", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "typescript": "^5.3.3"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.1", "@types/node": "20.5.7", "@types/react": "^19.0.1", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "autoprefixer": "10.4.19", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "8.4.38", "tailwindcss": "^3.4.17", "typescript": "5.0.4"}}