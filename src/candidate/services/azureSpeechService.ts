import config from '../config';

export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  offset: number;
  duration: number;
}

export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
  profanityFilter?: boolean;
  enableDictation?: boolean;
}

export class AzureSpeechService {
  private recognizer: any = null;
  private audioConfig: any = null;
  private speechConfig: any = null;
  private isListening: boolean = false;
  private onResultCallback?: (result: SpeechRecognitionResult) => void;
  private onErrorCallback?: (error: string) => void;
  private onStartCallback?: () => void;
  private onEndCallback?: () => void;

  constructor() {
    this.initializeSpeechSDK();
  }

  private initializeSpeechSDK(): void {
    if (typeof window !== 'undefined' && (window as any).SpeechSDK) {
      const { SpeechSDK } = window as any;
      this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
        config.azure.speechKey,
        config.azure.speechRegion
      );
      this.speechConfig.speechRecognitionLanguage = config.azure.speechRecognition?.language || 'ja-JP';
      this.speechConfig.enableDictation = config.azure.speechRecognition?.enableDictation || true;
    }
  }

  async startRecognition(
    onResult: (result: SpeechRecognitionResult) => void,
    onError?: (error: string) => void,
    onStart?: () => void,
    onEnd?: () => void
  ): Promise<boolean> {
    try {
      this.onResultCallback = onResult;
      this.onErrorCallback = onError;
      this.onStartCallback = onStart;
      this.onEndCallback = onEnd;

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      const { SpeechSDK } = window as any;
      this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(stream);
      this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);

      this.setupEventHandlers();
      this.recognizer.startContinuousRecognitionAsync();
      this.isListening = true;
      this.onStartCallback?.();

      return true;
    } catch (error) {
      this.onErrorCallback?.(`Speech recognition start failed: ${error}`);
      return false;
    }
  }

  private setupEventHandlers(): void {
    if (!this.recognizer) return;

    const { SpeechSDK } = window as any;

    this.recognizer.recognized = (s: any, e: any) => {
      if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
        this.onResultCallback?.({
          transcript: e.result.text,
          confidence: e.result.properties.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult),
          isFinal: true,
          offset: e.result.offset,
          duration: e.result.duration
        });
      }
    };

    this.recognizer.recognizing = (s: any, e: any) => {
      this.onResultCallback?.({
        transcript: e.result.text,
        confidence: 0.5, // Interim results don't have confidence
        isFinal: false,
        offset: e.result.offset,
        duration: e.result.duration
      });
    };

    this.recognizer.canceled = (s: any, e: any) => {
      this.onErrorCallback?.(`Recognition canceled: ${e.reason}`);
      this.isListening = false;
      this.onEndCallback?.();
    };

    this.recognizer.sessionStopped = (s: any, e: any) => {
      this.isListening = false;
      this.onEndCallback?.();
    };
  }

  stopRecognition(): void {
    if (this.recognizer && this.isListening) {
      this.recognizer.stopContinuousRecognitionAsync();
      this.isListening = false;
    }
  }

  isActive(): boolean {
    return this.isListening;
  }

  cleanup(): void {
    this.stopRecognition();
    if (this.recognizer) {
      this.recognizer.close();
      this.recognizer = null;
    }
    if (this.audioConfig) {
      this.audioConfig.close();
      this.audioConfig = null;
    }
  }
}

export const azureSpeechService = new AzureSpeechService();
