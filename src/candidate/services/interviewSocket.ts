// Interview Socket Service - Focused on socket communication only
export type SocketMessage = {
    type: string;
    message?: string | any;
    question_id?: string;
    video_chunk?: Blob;
    text?: string;
    voice?: string;
    audio?: string;
    error?: string;
    timestamp: string;
    question?: any;
    feedback?: any;
    results?: any[];
    // WebRTC signaling fields
    offer?: RTCSessionDescriptionInit;
    answer?: RTCSessionDescriptionInit;
    candidate?: RTCIceCandidate;
    sessionId?: string;
    [key: string]: any;

    transcript?: string;
    confidence?: number;
    isFinal?: boolean;
};

type MessageHandler = (message: SocketMessage) => void;
type ErrorHandler = (error: string) => void;
type StatusHandler = (connected: boolean) => void;

class InterviewSocketService {
    private socket: WebSocket | null = null;
    private sessionId: string = '';
    private messageHandlers: MessageHandler[] = [];
    private errorHandlers: ErrorHandler[] = [];
    private statusHandlers: StatusHandler[] = [];
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 3;
    private messageQueue: any[] = [];

    async connect(sessionId: string): Promise<boolean> {
        if (this.socket?.readyState === WebSocket.OPEN && this.sessionId === sessionId) {
            return true;
        }

        if (this.socket?.readyState === WebSocket.CONNECTING && this.sessionId === sessionId) {
            return new Promise((resolve) => {
                const checkConnection = () => {
                    if (this.socket?.readyState === WebSocket.OPEN) {
                        resolve(true);
                    } else if (this.socket?.readyState === WebSocket.CLOSED) {
                        resolve(false);
                    } else {
                        setTimeout(checkConnection, 100);
                    }
                };
                checkConnection();
            });
        }

        this.disconnect();
        this.sessionId = sessionId;

        return new Promise((resolve) => {
            const wsUrl = `${this.getWsUrl()}/ws/interview/${sessionId}`;
            this.socket = new WebSocket(wsUrl);

            this.socket.onopen = () => {
                this.reconnectAttempts = 0;
                this.notifyStatus(true);
                
                // Send queued messages
                while (this.messageQueue.length > 0) {
                    const queuedMessage = this.messageQueue.shift();
                    this.send(queuedMessage);
                }
                
                resolve(true);
            };

            this.socket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.notifyMessage(message);
                } catch {
                    this.notifyError('Parse error');
                }
            };

            this.socket.onclose = () => {
                this.notifyStatus(false);
                this.reconnect();
            };

            this.socket.onerror = () => {
                this.notifyError('Connection error');
                resolve(false);
            };
        });
    }

    send(message: any): boolean {
        const messageWithTimestamp = {
            ...message,
            timestamp: new Date().toISOString()
        };

        if (this.socket?.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(messageWithTimestamp));
            return true;
        } else {
            // Queue message if socket is not ready
            this.messageQueue.push(messageWithTimestamp);
            return false;
        }
    }

    disconnect(): void {
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
            this.socket.close();
        }
        this.socket = null;
        this.sessionId = '';
        this.reconnectAttempts = 0;
        this.messageQueue = [];
        this.notifyStatus(false);
    }

    // WebRTC signaling methods
    sendWebRTCOffer(offer: RTCSessionDescriptionInit): boolean {
        return this.send({
            type: 'webrtc_offer',
            offer: offer,
            sessionId: this.sessionId
        });
    }

    sendWebRTCAnswer(answer: RTCSessionDescriptionInit): boolean {
        return this.send({
            type: 'webrtc_answer',
            answer: answer,
            sessionId: this.sessionId
        });
    }

    sendWebRTCCandidate(candidate: RTCIceCandidate): boolean {
        return this.send({
            type: 'webrtc_candidate',
            candidate: candidate,
            sessionId: this.sessionId
        });
    }

    sendWebRTCReady(): boolean {
        return this.send({
            type: 'webrtc_ready',
            sessionId: this.sessionId
        });
    }

    onMessage(handler: MessageHandler): () => void {
        this.messageHandlers.push(handler);
        return () => {
            const index = this.messageHandlers.indexOf(handler);
            if (index > -1) this.messageHandlers.splice(index, 1);
        };
    }

    onError(handler: ErrorHandler): () => void {
        this.errorHandlers.push(handler);
        return () => {
            const index = this.errorHandlers.indexOf(handler);
            if (index > -1) this.errorHandlers.splice(index, 1);
        };
    }

    onStatus(handler: StatusHandler): () => void {
        this.statusHandlers.push(handler);
        return () => {
            const index = this.statusHandlers.indexOf(handler);
            if (index > -1) this.statusHandlers.splice(index, 1);
        };
    }

    private notifyMessage(message: SocketMessage): void {
        this.messageHandlers.forEach(handler => handler(message));
    }

    private notifyError(error: string): void {
        this.errorHandlers.forEach(handler => handler(error));
    }

    private notifyStatus(connected: boolean): void {
        this.statusHandlers.forEach(handler => handler(connected));
    }

    private reconnect(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts && this.sessionId) {
            this.reconnectAttempts++;
            const backoffDelay = 1000 * Math.pow(2, this.reconnectAttempts - 1);

            setTimeout(() => {
                if (this.sessionId && (!this.socket || this.socket.readyState === WebSocket.CLOSED)) {
                    this.connect(this.sessionId).then(success => {
                        if (success) {
                            console.log('Reconnection successful');
                        } else {
                            console.log('Reconnection failed, will retry if attempts remain');
                        }
                    });
                }
            }, backoffDelay);
        } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.notifyError('Connection failed after maximum retries. Please refresh the page to continue.');
        }
    }

    private getWsUrl(): string {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        return `${protocol}//localhost:8080`;
    }

    private sendSpeechResponse(transcript: string, confidence: number, isFinal: boolean): boolean {
        return this.send({
            type: 'speech_response',
            transcript: transcript,
            confidence: confidence,
            isFinal: isFinal
        });
    }

    private sendSpeechStart(): boolean {
        return this.send({
            type: 'speech_start'
        });
    }

    private sendSpeechEnd(): boolean {
        return this.send({
            type: 'speech_end'
        });
    }
}

export const interviewSocket = new InterviewSocketService(); 