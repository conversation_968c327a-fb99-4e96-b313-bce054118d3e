import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
  useRef,
} from 'react';
import { interviewSocket, SocketMessage } from '../services/interviewSocket';
import { TTSService, TTSResponse } from '../services/ttsService';

interface InterviewState {
  sessionId: string;
  isConnected: boolean;
  currentMessage: SocketMessage | null;
  lastError: string | null;
}

interface InterviewContextType extends InterviewState {
  connect: (sessionId: string) => Promise<boolean>;
  send: (message: any) => boolean;
  disconnect: () => void;
  clearError: () => void;
  // TTS methods
  speakText: (text: string, voice?: string) => void;
}

const InterviewContext = createContext<InterviewContextType | null>(null);

export const InterviewProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, setState] = useState<InterviewState>({
    sessionId: '',
    isConnected: false,
    currentMessage: null,
    lastError: null,
  });

  const isConnectedRef = useRef(false);
  const currentSessionRef = useRef('');
  const ttsServiceRef = useRef<TTSService | null>(null);

  // Initialize TTS service
  useEffect(() => {
    ttsServiceRef.current = new TTSService((message) =>
      interviewSocket.send(message)
    );
  }, []);

  useEffect(() => {
    const unsubscribeMessage = interviewSocket.onMessage((message) => {
      // Handle TTS responses
      if (message.type === 'tts_response' || message.type === 'tts_error') {
        ttsServiceRef.current?.handleTTSResponse(message as TTSResponse);
        return;
      }

      // Handle other messages
      setState((prev) =>
        prev.currentMessage?.timestamp !== message.timestamp
          ? { ...prev, currentMessage: message }
          : prev
      );
    });

    const unsubscribeError = interviewSocket.onError((error) => {
      setState((prev) =>
        prev.lastError !== error ? { ...prev, lastError: error }
        : prev
      );
    });

    const unsubscribeStatus = interviewSocket.onStatus((connected) => {
      if (isConnectedRef.current !== connected) {
        isConnectedRef.current = connected;
        setState((prev) => ({ ...prev, isConnected: connected }));
      }
    });

    return () => {
      unsubscribeMessage();
      unsubscribeError();
      unsubscribeStatus();
    };
  }, []);

  const connect = useCallback(async (sessionId: string): Promise<boolean> => {
    if (currentSessionRef.current === sessionId && isConnectedRef.current) {
      return true;
    }

    currentSessionRef.current = sessionId;
    setState((prev) => ({ ...prev, sessionId, lastError: null }));
    return await interviewSocket.connect(sessionId);
  }, []);

  const send = useCallback((message: any): boolean => {
    return interviewSocket.send(message);
  }, []);

  const disconnect = useCallback((): void => {
    interviewSocket.disconnect();
    currentSessionRef.current = '';
    isConnectedRef.current = false;
    setState((prev) => ({
      ...prev,
      sessionId: '',
      currentMessage: null,
      isConnected: false,
    }));
    ttsServiceRef.current?.clearCache();
  }, []);

  const clearError = useCallback((): void => {
    setState((prev) => ({ ...prev, lastError: null }));
  }, []);

  // TTS method
  const speakText = useCallback(
    (text: string, voice: string = 'ja-JP-NanamiNeural'): void => {
      if (isConnectedRef.current && ttsServiceRef.current) {
        ttsServiceRef.current.requestSpeech(text, voice);
      }
    },
    []
  );

  return (
    <InterviewContext.Provider
      value={{
        ...state,
        connect,
        send,
        disconnect,
        clearError,
        speakText,
      }}
    >
      {children}
    </InterviewContext.Provider>
  );
};

// Single hook for accessing interview context
export const useInterview = () => {
  const context = useContext(InterviewContext);
  if (!context) {
    throw new Error('useInterview must be used within InterviewProvider');
  }
  return context;
};
