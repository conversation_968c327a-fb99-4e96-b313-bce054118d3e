/**
 * Azure Speech SDK の型定義 - Optimized Version
 * Microsoftの公式SDKに対する型安全性を提供
 * Apply DRY principles and TypeScript utility types
 */


interface BaseTimestamped {
  timestamp: number;
}

interface BaseSpeechData {
  confidence: number;
  isFinal: boolean;
}

type MessageType =
  | 'videoFrameReceived'
  | 'speechStarted'
  | 'speechEnded'
  | 'connectionEstablished'
  | 'connectionClosed'
  | 'response_answer'
  | 'speech_start'
  | 'speech_end'
  | 'speech_interim';


export interface SpeechConfig {
  speechSynthesisVoiceName: string;
}

export interface AvatarVideoFormat {
}

export interface AvatarConfig {
  backgroundColor: string;
}

export interface AvatarEvent extends BaseTimestamped {
  type: Extract<MessageType, 'videoFrameReceived' | 'speechStarted' | 'speechEnded' | 'connectionEstablished' | 'connectionClosed'>;
  data?: {
    mediaStream?: MediaStream;
    message?: string;
    timestamp?: number;
  };
}

export interface ChatAvatarConnection {
  avatarEventReceived: ((event: AvatarEvent) => void) | null;
  start(): Promise<void>;
  close(): void;
  speakTextAsync(text: string): Promise<void>;
}

export interface SpeechSDKStatic {
  SpeechConfig: {
    fromSubscription(key: string, region: string): SpeechConfig;
  };
  AvatarVideoFormat: new () => AvatarVideoFormat;
  AvatarConfig: new (avatarId: string, style: string, videoFormat: AvatarVideoFormat) => AvatarConfig;
  ChatAvatarConnection: new (speechConfig: SpeechConfig, avatarConfig: AvatarConfig) => ChatAvatarConnection;
}

export interface SpeechRecognitionResult extends BaseSpeechData, BaseTimestamped {
  transcript: string;
  offset: number;
  duration: number;
}

export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
  profanityFilter?: boolean;
  enableDictation?: boolean;
}

interface BaseSocketMessage extends BaseTimestamped {
  type: MessageType;
  sessionId?: string;
}

export interface SpeechSocketMessage extends BaseSocketMessage {
  type: Extract<MessageType, 'response_answer' | 'speech_start' | 'speech_end' | 'speech_interim'>;
  transcript?: string;
  confidence?: number;
  isFinal?: boolean;
}

export type CreateMessage<T extends MessageType> = BaseSocketMessage & {
  type: T;
};

export type SpeechMessageWithData<T extends Extract<MessageType, 'response_answer' | 'speech_start' | 'speech_end' | 'speech_interim'>> =
  CreateMessage<T> & Pick<SpeechRecognitionResult, 'transcript' | 'confidence' | 'isFinal'>;

declare global {
  interface Window {
    SpeechSDK: SpeechSDKStatic;
  }
}

export function isAvatarEvent(event: AvatarEvent | SpeechSocketMessage): event is AvatarEvent {
  return ['videoFrameReceived', 'speechStarted', 'speechEnded', 'connectionEstablished', 'connectionClosed'].includes(event.type);
}

export function isSpeechSocketMessage(message: AvatarEvent | SpeechSocketMessage): message is SpeechSocketMessage {
  return ['response_answer', 'speech_start', 'speech_end', 'speech_interim'].includes(message.type);
}
